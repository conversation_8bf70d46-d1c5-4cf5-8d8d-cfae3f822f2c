import * as DAT5 from 'api2dat5'
import { format, parse } from 'date-fns'
import { SingleProfileData } from 'src/profiles/types'
import { findSectionIgnoreCase } from './findSectionIgnoreCase'
import { findKeyValueIgnoreCase } from './findKeyValueIgnoreCase'

function formatFloat(
  value?: string,
  options?: { thousandSeparator?: '.' | ',' | ' '; floatDecimalSeparator?: '.' | ','; floatFixedDecimalScale?: number }
): string {
  if (!value || typeof value !== 'string') return ''
  value = value.replace(/,/g, '.')
  const floatNumber = parseFloat(value)
  if (Number.isNaN(floatNumber)) return ''

  const { thousandSeparator, floatDecimalSeparator, floatFixedDecimalScale } = options || {}
  let result = floatNumber.toString()
  if (floatFixedDecimalScale) result = floatNumber.toFixed(floatFixedDecimalScale)
  if (floatDecimalSeparator) result = result.replace('.', floatDecimalSeparator)
  if (thousandSeparator) result = result.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator)

  return result
}

function formatInteger(value?: string, options?: { thousandSeparator?: '.' | ',' | ' ' }): string {
  if (!value || typeof value !== 'string') return ''
  const integer = parseInt(value)
  if (Number.isNaN(integer)) return ''

  const { thousandSeparator } = options || {}
  if (thousandSeparator) return integer.toString().replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator)
  return integer.toString()
}

export function parseValue(value: string | string[], config: DAT.IniMappingSubfield) {
  const {
    type,
    floatFixedDecimalScale,
    floatDecimalSeparator,
    thousandSeparator,
    initialDateFormat,
    dateFormat,
    index
  } = config
  let result = Array.isArray(value) ? value[index || 0] : value

  switch (type) {
    case 'date':
      let parsedDate = new Date(result)
      if (initialDateFormat) parsedDate = parse(result, initialDateFormat, new Date())
      const isInvalidDate = isNaN(parsedDate.getTime())
      if (isInvalidDate) result = ''
      if (isInvalidDate) break
      if (dateFormat) result = format(parsedDate, dateFormat)
      break
    case 'integer':
      result = formatInteger(result, { thousandSeparator })
      break
    case 'float':
      result = formatFloat(result, { thousandSeparator, floatDecimalSeparator, floatFixedDecimalScale })
  }

  return result
}

function addValueToPayload(
  value: string,
  config: DAT.IniMappingSubfield,
  payloadForCreateOrUpdateContract: DAT5.MyClaimExternalService_schema1.createOrUpdateContract
) {
  if (value === '') return payloadForCreateOrUpdateContract
  const { dossierData, templateData, memoFieldType = 'xs:string' } = config

  if (templateData) {
    if (!payloadForCreateOrUpdateContract.templateData.entry) payloadForCreateOrUpdateContract.templateData.entry = []
    payloadForCreateOrUpdateContract.templateData.entry.push({
      key: templateData,
      value: { _value: value, _attr_type: memoFieldType }
    })
  }

  // dossierData = Ex. 'Vehicle.RegistrationData.NumberPlate' or 'Vehicle.DatECodeEquipment.[0].EquipmentId'
  if (dossierData) {
    const pathParts = dossierData.split('.')
    let current = payloadForCreateOrUpdateContract.Dossier

    for (let i = 0; i < pathParts.length; i++) {
      const key = pathParts[i]
      const isLastKey = i === pathParts.length - 1
      const arrayIndexMatch = key.match(/^\[(\d+)]$/)
      const arrayIndex = arrayIndexMatch ? Number(arrayIndexMatch[1]) : null

      if (arrayIndex !== null) {
        if (isLastKey) {
          if (!Array.isArray(current)) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            current = []
          }
          current[arrayIndex] = value
        } else {
          if (!Array.isArray(current)) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            current = []
          }
          if (!current[arrayIndex]) {
            current[arrayIndex] = {}
          }
          current = current[arrayIndex]
        }
      } else {
        if (isLastKey) {
          current[key] = value
        } else {
          const nextKey = pathParts[i + 1]
          const nextIsArrayIndex = nextKey && nextKey.match(/^\[(\d+)]$/)

          if (!current[key]) {
            current[key] = nextIsArrayIndex ? [] : {}
          }
          current = current[key]
        }
      }
    }
  }

  return payloadForCreateOrUpdateContract
}

export function createPayloadForCreateContractFromIniData(
  iniData: Record<string, Array<Record<string, string | string[]>>>,
  iniConfig: DAT.IniMappingContent | null,
  contractBaseData: {
    locale: DAT.Locale
    contractType: DAT.ContractType
    networkType: DAT.NetworkType
    country: DAT.CountryCode
    currency: string
    userProfile: SingleProfileData
    username: string
    claimNumberMemoFieldName: string
    claimNumber: string
  },
  templateId?: number
) {
  if (!iniConfig?.fields) return null
  const { fields, contractBaseData: contractBaseDataFromConfig } = iniConfig
  const {
    userProfile: { name = '', surname = '', code = '', email = '' },
    username,
    country,
    currency,
    contractType,
    networkType,
    locale,
    claimNumberMemoFieldName,
    claimNumber
  } = contractBaseData

  const [formattedToday, formattedFullDate] = format(new Date(), 'dd.MM.yy|dd.MM.yy H:mm:ss').split('|')

  const payloadForCreateOrUpdateContract: DAT5.MyClaimExternalService_schema1.createOrUpdateContract = {
    Dossier: {
      Country: templateId ? country : contractBaseDataFromConfig?.country || country || '',
      Language: templateId ? locale : contractBaseDataFromConfig?.language || locale || '',
      Currency: templateId ? currency : contractBaseDataFromConfig?.currency || currency
    },
    templateData: {
      entry: [
        { key: 'memoWithSplitBySeparatorIdx0', value: { _value: 'COMINC', _attr_type: 'xs:string' } },

        {
          key: 'creationDate',
          value: { _value: formattedToday, _attr_type: 'xs:string' }
        },
        {
          key: 'createdByName',
          value: { _value: `${name} ${surname}`, _attr_type: 'xs:string' }
        },
        {
          key: 'createdByAlias',
          value: { _value: username, _attr_type: 'xs:string' }
        },
        {
          key: 'createdByWeDat',
          value: { _value: formattedFullDate, _attr_type: 'xs:string' }
        },
        {
          key: claimNumberMemoFieldName,
          value: { _value: claimNumber, _attr_type: 'xs:string' }
        },
        ...(code ? [{ key: 'createdByCode', value: { _value: code, _attr_type: 'xs:string' } }] : []),
        ...(email ? [{ key: 'createdByMail', value: { _value: email, _attr_type: 'xs:string' } }] : [])
      ]
    },
    networkType: templateId ? networkType : contractBaseDataFromConfig?.networkType || networkType,
    contractType: templateId ? contractType : contractBaseDataFromConfig?.contractType || contractType,
    templateId: templateId || contractBaseDataFromConfig?.templateId
  }

  for (const field of fields) {
    const { iniSection, iniKey, separator, subfields } = field
    if (!iniSection || !iniKey || !subfields) continue

    const section = findSectionIgnoreCase(iniData, iniSection)
    if (!section) continue

    const value = findKeyValueIgnoreCase(section, iniKey)
    if (!value) continue

    if (Array.isArray(value)) {
      subfields.forEach(subfield => {
        const parsedValue = parseValue(value, subfield)
        addValueToPayload(parsedValue, subfield, payloadForCreateOrUpdateContract)
      })
    } else {
      if (separator) {
        const splitValue = value.split(separator)
        subfields.forEach(subfield => {
          const parsedValue = parseValue(splitValue, subfield)
          addValueToPayload(parsedValue, subfield, payloadForCreateOrUpdateContract)
        })
      } else {
        const subfield = subfields[0] || {}
        const parsedValue = parseValue(value, subfield)
        addValueToPayload(parsedValue, subfield, payloadForCreateOrUpdateContract)
      }
    }
  }

  return payloadForCreateOrUpdateContract
}
