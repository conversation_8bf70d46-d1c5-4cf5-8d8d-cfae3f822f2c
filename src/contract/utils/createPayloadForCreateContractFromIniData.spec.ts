import { parseValue, createPayloadForCreateContractFromIniData } from './createPayloadForCreateContractFromIniData'
import { detectIniConfig } from './detectIniConfig'
import { parseIniFile } from './parseIniFile'
import {
  MOCK_INI_DATA_FOR_ALLIANZ,
  MOCK_INI_DATA_FOR_SOGESA,
  MOCK_INI_DATA_FOR_AXA,
  MOCK_MAPPING_CONFIG
} from '../constants'
import { SingleProfileData } from 'src/profiles/types'

describe('parseValue', () => {
  it('should correct parse float value', () => {
    const config: DAT.IniMappingSubfield = {
      type: 'float',
      floatFixedDecimalScale: 2,
      floatDecimalSeparator: ',',
      thousandSeparator: '.'
    }
    expect(parseValue('1000', config)).toBe('1.000,00')
    expect(parseValue('10000000', config)).toBe('10.000.000,00')
    expect(parseValue('1000.12', config)).toBe('1.000,12')
    expect(parseValue(['1000', '1234'], config)).toBe('1.000,00')
    expect(parseValue(['1000', '1234'], { ...config, index: 1 })).toBe('1.234,00')
    expect(parseValue('test', config)).toBe('')
    expect(parseValue(NaN as unknown as string, config)).toBe('')
  })

  it('should correct parse integer value', () => {
    const config: DAT.IniMappingSubfield = {
      type: 'integer',
      thousandSeparator: '.'
    }
    expect(parseValue('1000', config)).toBe('1.000')
    expect(parseValue('10000000', config)).toBe('10.000.000')
    expect(parseValue('1000.12', config)).toBe('1.000')
    expect(parseValue(['1000', '1234'], config)).toBe('1.000')
    expect(parseValue(['1000', '1234'], { ...config, index: 1 })).toBe('1.234')
    expect(parseValue('test', config)).toBe('')
    expect(parseValue(NaN as unknown as string, config)).toBe('')
  })

  it('should correct parse date value', () => {
    const config: DAT.IniMappingSubfield = {
      type: 'date',
      dateFormat: 'dd.MM.yyyy'
    }
    expect(parseValue('01/02/2001', { ...config, initialDateFormat: 'dd/MM/yyyy' })).toBe('01.02.2001')
    expect(parseValue('01.02.2001', { ...config, initialDateFormat: 'dd.MM.yyyy' })).toBe('01.02.2001')
    expect(parseValue('01022001', { ...config, initialDateFormat: 'ddMMyyyy' })).toBe('01.02.2001')
    expect(parseValue('asdasds', { ...config, initialDateFormat: 'ddMMyyyy' })).toBe('')
  })
})

describe('createPayloadForCreateContractFromIniData', () => {
  const contractBaseData = {
    userProfile: {
      name: 'Simone',
      surname: 'Ferreto',
      code: '11111',
      email: '<EMAIL>'
    } as unknown as SingleProfileData,
    username: 'ferrsimo',
    country: 'IT' as DAT.CountryCode,
    currency: 'EUR',
    contractType: 'vro_calculation' as DAT.ContractType,
    networkType: 'IFERT' as DAT.NetworkType,
    locale: 'it-IT' as DAT.Locale,
    claimNumberMemoFieldName: 'claimNumber',
    claimNumber: 'ferrsimo:20240101'
  }

  it('should create correct contract payload for ALLIANZ ini', () => {
    const parsedIni = parseIniFile(MOCK_INI_DATA_FOR_ALLIANZ)
    const detectedConfig = detectIniConfig(parsedIni, MOCK_MAPPING_CONFIG)
    const payload = createPayloadForCreateContractFromIniData(parsedIni, detectedConfig, contractBaseData)

    expect(payload).toStrictEqual({
      Dossier: { Country: 'IT', Language: 'it-IT', Currency: 'EUR' },
      templateData: {
        entry: [
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: 'COMINC', _attr_type: 'xs:string' } },
          { key: 'creationDate', value: { _value: '06.07.25', _attr_type: 'xs:string' } },
          { key: 'createdByName', value: { _value: 'Simone Ferreto', _attr_type: 'xs:string' } },
          { key: 'createdByAlias', value: { _value: 'ferrsimo', _attr_type: 'xs:string' } },
          { key: 'createdByWeDat', value: { _value: '06.07.25 22:14:53', _attr_type: 'xs:string' } },
          { key: 'claimNumber', value: { _value: 'ferrsimo:20240101', _attr_type: 'xs:string' } },
          { key: 'createdByCode', value: { _value: '11111', _attr_type: 'xs:string' } },
          { key: 'createdByMail', value: { _value: '<EMAIL>', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: '01', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx1', value: { _value: 'PERIZIA AUTO STANDARD', _attr_type: 'xs:string' } }
        ]
      },
      networkType: 'IFERT',
      contractType: 'vro_calculation',
      templateId: 111111
    })
  })

  it('should create correct contract payload for SOGESA ini', () => {
    const parsedIni = parseIniFile(MOCK_INI_DATA_FOR_SOGESA)
    const detectedConfig = detectIniConfig(parsedIni, MOCK_MAPPING_CONFIG)
    const payload = createPayloadForCreateContractFromIniData(parsedIni, detectedConfig, contractBaseData, 121212)

    expect(payload).toStrictEqual({
      Dossier: { Country: 'IT', Language: 'it-IT', Currency: 'EUR' },
      templateData: {
        entry: [
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: 'COMINC', _attr_type: 'xs:string' } },
          { key: 'creationDate', value: { _value: '06.07.25', _attr_type: 'xs:string' } },
          { key: 'createdByName', value: { _value: 'Simone Ferreto', _attr_type: 'xs:string' } },
          { key: 'createdByAlias', value: { _value: 'ferrsimo', _attr_type: 'xs:string' } },
          { key: 'createdByWeDat', value: { _value: '06.07.25 22:15:44', _attr_type: 'xs:string' } },
          { key: 'claimNumber', value: { _value: 'ferrsimo:20240101', _attr_type: 'xs:string' } },
          { key: 'createdByCode', value: { _value: '11111', _attr_type: 'xs:string' } },
          { key: 'createdByMail', value: { _value: '<EMAIL>', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: '01', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx1', value: { _value: 'PERITO', _attr_type: 'xs:string' } }
        ]
      },
      networkType: 'IFERT',
      contractType: 'vro_calculation',
      templateId: 121212
    })
  })

  it('should create correct contract payload for AXA ini', () => {
    const parsedIni = parseIniFile(MOCK_INI_DATA_FOR_AXA)
    const detectedConfig = detectIniConfig(parsedIni, MOCK_MAPPING_CONFIG)
    const payload = createPayloadForCreateContractFromIniData(parsedIni, detectedConfig, contractBaseData)

    expect(payload).toStrictEqual({
      Dossier: {
        Country: 'IT',
        Language: 'it-IT',
        Currency: 'EUR',
        Vehicle: {
          SomeTestField: '410166298',
          SomeTestArrStr: ['410 166 298'],
          SomeTestArrObj: [{ float: '410 166 298,00' }]
        }
      },
      templateData: {
        entry: [
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: 'COMINC', _attr_type: 'xs:string' } },
          { key: 'creationDate', value: { _value: '06.07.25', _attr_type: 'xs:string' } },
          { key: 'createdByName', value: { _value: 'Simone Ferreto', _attr_type: 'xs:string' } },
          { key: 'createdByAlias', value: { _value: 'ferrsimo', _attr_type: 'xs:string' } },
          { key: 'createdByWeDat', value: { _value: '06.07.25 22:15:44', _attr_type: 'xs:string' } },
          { key: 'claimNumber', value: { _value: 'ferrsimo:20240101', _attr_type: 'xs:string' } },
          { key: 'createdByCode', value: { _value: '11111', _attr_type: 'xs:string' } },
          { key: 'createdByMail', value: { _value: '<EMAIL>', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: 'COMINC', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx1', value: { _value: 'Incarico di Perizia', _attr_type: 'xs:string' } },
          { key: 'memoWithFormattedDate', value: { _value: '27.12.2024', _attr_type: 'xs:date' } },
          { key: 'memoWithInteger', value: { _value: '410166298', _attr_type: 'xs:integer' } },
          { key: 'memoWithIntegerWithThousandSeparator', value: { _value: '410 166 298', _attr_type: 'xs:integer' } },
          { key: 'memoWithFloatWithThousandSeparator', value: { _value: '410 166 298,00', _attr_type: 'xs:decimal' } }
        ]
      },
      networkType: 'IFERT',
      contractType: 'vro_calculation',
      templateId: 222222
    })
  })
})
