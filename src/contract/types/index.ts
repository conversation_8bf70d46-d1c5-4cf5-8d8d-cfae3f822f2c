import * as DAT5 from 'api2dat5'
import { RequiredKeys } from 'src/common/types/utility'

export type PayloadForUpdateContract =
  | RequiredKeys<DAT.Request.CreateOrUpdateContract, 'contractId'> // you can either use default `CreateOrUpdateContract` payload
  | (Partial<DAT.Request.CreateOrUpdateContract> & {
      contract: DAT.ContractFromGetContract // or you can pass already fetched contract instead of specifying every single property (contractType, contractId, etc.)
    })

export interface PayloadForExtractEntry {
  contract: DAT.ContractFromGetContract
  key: string
}

export interface PayloadForPhotoUpload {
  customerNumber: number
  contractId: number
  attachmentItem: DAT.AttachmentItem
}

export interface PayloadForCreateClaimWithAnotherAccount {
  claimData: DAT.Request.CreateOrUpdateContract
  tagId: number
  tags: PayloadForCreateClaimWithAnotherAccountTag[]
}

export interface CreateClaimWithAnotherAccountResponse {
  claimId: number
  assignedTag?: PayloadForCreateClaimWithAnotherAccountTag
  calcResult: DAT.Response.CalculateContractN
}

export type PayloadForCreateClaimWithAnotherAccountTag = {
  id: number
  label: string
  color: string
  icon: boolean
}

export interface ImportIniResult {
  parsedIni?: Record<string, Array<Record<string, string | string[]>>>
  detectedConfig?: DAT.IniMappingContent | null
  payloadForCreateContract?: DAT5.MyClaimExternalService_schema1.createOrUpdateContract | null
  error?: {
    errorCode: number
    errorMessage: string
  }
  contractId?: number
}
