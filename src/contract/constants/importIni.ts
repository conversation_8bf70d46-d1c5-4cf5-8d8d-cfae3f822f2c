export const MOCK_INI_DATA_FOR_AXA = `
[MESSAGGIO]
Versione=1.0
Compagnia=014~AXA ASSICURAZIONI                  
TipoMessaggio=COMINC~Incarico di Perizia
IDMessaggio=9CD7F740-ACCD-43FE-ACE4-48C5C728D8C7
RicevutaRitorno=N
CodiceSoftware=
[INCARICO]
TipoIncarico=01~Auto - P.L.
DataIncarico=27/12/2024
NumeroSinistro=5601-99-016094
TipoSinistro=03~FURTO
AnnoEsercizio=2024
TipoBeneDanneggiato=
LuogoAvvenimento=FROSINONE~03100~FR
DataAvvenimento=20/12/2024
OraAvvenimento=
NumeroPolizzaCliente=410166298
AgenziaPolizzaCliente=5601~FROSINONE
RamoPolizzaCliente=
RegolaritaContrattuale=
ValoreAssicurato=
Franchigia=
PercentualeScoperto=
DatiAnagraficiCliente=RINALDI~GIUSEPPE
CodiceFiscaleCliente=
IndirizzoCliente=VIA VITTORIO GASSMAN 6~FROSINONE~03100~FR
RecapitoCliente=
TipoVeicoloCliente=
DatiVeicoloCliente=GN119MM~
IndirizzoVisibilitaVeicCli=
DanniDichiaratiVeicCli1=
DanniDichiaratiVeicCli2=
PuntiUrtoVeicCli1=
CompagniaPolizzaControparte=
NumeroPolizzaContoparte=
AgenziaPolizzaControparte=
DatiAnagraficiControparte=~
IndirizzoControparte=~~~
RecapitoControparte=
TipoVeicoloControparte=
DatiVeicoloCPT=
IndirizzoVisibilitaVeicCtp=
TipoLuogoRiparazione=
DinamicaIncidente=
CasiCIDCliente=
CasiCIDCTP=
ResponsabilitaCliente=
NumeroDanneggiati=
EsistonoLesioni=
Legale=
AutoritaIntervenute=
Ispettorato=60~SC Pronta Liquidazione
TargaVeicoloDaPeriziare=GN119MM
Note=
Perizia=
FiduciarioCodCompagnia=10889~ESTEEM SRL                    
[Allegati]
File1=
`
export const MOCK_INI_DATA_FOR_ALLIANZ = `
[MESSAGGIO]
Versione=2.0
Compagnia=352~ALLIANZ VIVA
TipoMessaggio=COMINC~INCARICO DI PERIZIA
CodiceMandato=434136563539580
IDMessaggio=
RicevutaRitorno=S
CodiceSoftware=

[INCARICO]
TipoIncarico=01~PERIZIA AUTO STANDARD
DataIncarico=09/12/2024
NumeroSinistro=14/600190433-4/1
TipoSinistro=02~CID TOTALE
AnnoEsercizio=2024
TipoBeneDanneggiato=
LuogoAvvenimento=VIA LEONARDO DA VINCI~ROMA~  100~RM
DataAvvenimento=27/09/2024
OraAvvenimento=17.38.00
NumeroPolizzaCliente=537732094
AgenziaPolizzaCliente=0002361~CHERUBINI ASSICURAZIONI
RamoPolizzaCliente=031~
RegolaritaContrattuale=0~SINISTRO REGOLARE                 000000
ValoreAssicurato=
Franchigia=~  0
PercentualeScoperto=
MinScoperto=~  0
DatiAnagraficiCliente=~TECHNODAL
CodiceFiscaleCliente=
IndirizzoCliente=LARGO A VESSELLA 27~ROMA~  199~RM
RecapitoCliente=86200258
TipoVeicoloCliente=2~VEICOLO INDUSTRIALE
DatiVeicoloCliente=FV107VF~FIAT DOBLO'
IndirizzoVisibilitaVeicCli=VIA A VESSELLA 27~MONTEROTONDO~  015~RM
CompagniaPolizzaControparte=038~VITTORIA ASSICURAZIONI SPA
NumeroPolizzaControparte=00000000000000000000
AgenziaPolizzacontroparte=
DatiAnagraficicontroparte=~
IndirizzoControparte=~~~
RecapitoControparte=
TipoVeicoloControparte=1~AUTOVETTURA
DatiVeicoloCTP=EA701JM~FORD CNG TECHNIK DA3LPG S
IndirizzoVisibilitaVeicCtp=~~~
TipoluogoRiparazione=
DinamicaIncidente=
CasiCIDCliente=
CasiCIDCTP=
ResponsabilitaCliente=
NumeroDanneggiati=
EsistonoLesioni=
Legale=
AutoritaIntervenute=
Ispettorato=~DELOITTEBS UDB11~94443600~
TargaVeicoloDaPeriziare=FV107VF
Note=PL COLLABORATORI ON LINE - DA RESTITUIRE ENTRO IL : 2025-01-02PRESSO RIPARATORE PER VALUTAZIONE RIPAZIONI                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     - LIQ.:RADEGLIA LAURA                              - TEL.:94443600
Perizia=~
Documento1=ALLEGATI.File1
Documento2=ALLEGATI.File2
Documento3=ALLEGATI.File3

[ALLEGATI]
File1=All_14-600190433-1-1-3.pdf~RICHIESTA DANNI del 10-10-2024 - 10:58
File2=All_14-600190433-1-1-10.pdf~RICHIESTA DANNI del 14-10-2024 - 12:25
File3=All_14-600190433-1-1-21.pdf~ACQUISIZIONE ATTI del 09-12-2024 - 10:35
`
export const MOCK_INI_DATA_FOR_SOGESA = `
[INFO]
TipoMessaggio = 01~INCARICO DI PERIZIA
IdMessaggio = B6BB3B14-5BCE-95BA-9C0C-FAC49BE1A2D1
RicevutaRitorno = S
Versione = 1.5~01/02/2001
Valuta = 02~EURO

[MITTENTE]
TipoMittente = 03~COMPAGNIA
Nome = ~~So.Ge.S.A. S.R.L.
Indirizzo = VIA TRIESTE, 10~CATANIA~95127~CT
Email = <EMAIL>

[DESTINATARIO]
TipoDestinatario = 01~PERITO
Nome = ~ESTEEM S.R.L.~
Indirizzo = VIA L.F. DE MAGISTRIS~ROMA~00176~RM
Telefono = 0677077742
Fax = 0623328311
IdFid = 0024
Email = <EMAIL>

[INCARICO]
TipoIncarico = 01~PERIZIA AUTO
IdIncarico = 432v009yi6
DataIncarico = 19/12/2024
IndicatoreAntifrode = 0
Compagnia = 432~VERTI ASSICURAZIONI S.P.A.
Ispettorato = 432~NESSUNO
Liquidatore = ~Raffaella Giordano~~
NumeroDanneggiati = 1
ProgControparte = 1
PresenzaLesioni = N
Legale = ~~~~~
Autorita = 
EffettuarePL = N
NotePL = stl Gambacorta-Berardi 06 86603568 vhl c/o studio  h9.30-13/15.30-19
TipoSinistro = 16~CARD monofirma
NumeroSinistro = 3633312
NumeroSinistroParcella = 
LuogoSinistro = piazza di Tor Sanguigna null~ROMA~00186~RM
DataSinistro = 10/10/2024
OraSinistro = 02:00
DataDenuncia = 11/10/2024
EsercizioGestione = 2024
NomeAssicurato = ANTONELLA~FRANGIAMONE
IndirizzoAssicurato = VIA DI FEBO 1~ROMA~00186~RM
TelefonoAssicurato = 3284141521
CodiceFiscaleAssicurato = 
NumeroPolizzaAssicurato = DLI0504534451-
RamoPolizzaAssicurato = 
ModelloPolizzaAssicurato = 
DescrizionePolizzaAssicurato = 
DescrizioneGaranziaAssicurato = 
AgenziaPolizzaAssicurato = ~
TipoContratto = 
RegolaritaContrattuale = 
ValoreAssicurato = 27000,00
LimitazioneContratto = 
EstensioniPoliza = 
VeicoloAssicurato = GJ184BP~2022 JEEP RENEGADE (GJ184BP)
TelaioVeicoloAssicurato = 1C4NJCEA4NPN614
LuogoPeriziaAssicurato = ~ROMA~00186~RM
DescrizioneDinamicaAssicurato = 
DescrizioneDanniAssicurato = 
VincoloFinanziaria = 
NomeControparte = ASTRA S.P.A.~GRUPPO
IndirizzoControparte = VIA PIETRO MARONCELLI 71~MARINO~00047~RM
TelefonoControparte = 
CompagniaControparte = 34~
NumeroPolizzaControparte = 
AgenziaPolizzaControparte = ~
VeicoloControparte = GL199PK~2022 IVECO DAILY (2014---) (GL199PK / Reggio Em...
DescrizioneDinamicaControparte = 
DescrizioneDanniControparte = 
Note = REINCARICO  RFS: STIMARE A MO  37,1 ¤…/H – MUC … 17,7 ¤/H +SCONTO SU RICAMBI 12% VEDI CIRCOLARE  VEDI COMUNICAZIONE
DataAppuntamento = 
NoteAppuntamento = 
Authority = N
RiparatoreConvenzionato = ~
CodiceFiscaleControparte = 
TelaioVeicoloControparte = ZCFCE35B0054973
`

export const MOCK_MAPPING_CONFIG: DAT.IniMappingMemoField = {
  iniMapping: [
    {
      companyData: {
        id: '014',
        description: 'AXA ASSICURAZIONI',
        section: 'MESSAGGIO',
        key: 'Compagnia',
        separator: '~',
        index: 0
      },
      contractBaseData: {
        contractType: 'vro_calculation',
        country: 'IT',
        language: 'it-IT',
        networkType: 'IFERT',
        templateId: 222222
      },
      fields: [
        {
          iniSection: 'MESSAGGIO',
          iniKey: 'tipomessaggio',
          separator: '~',
          subfields: [
            {
              index: 0,
              type: 'string',
              templateData: 'memoWithSplitBySeparatorIdx0'
            },
            {
              index: 1,
              type: 'string',
              templateData: 'memoWithSplitBySeparatorIdx1'
            }
          ]
        },
        {
          iniSection: 'INCARICO',
          iniKey: 'DataIncarico',
          subfields: [
            {
              type: 'date',
              initialDateFormat: 'dd/MM/yyyy',
              dateFormat: 'dd.MM.yyyy',
              memoFieldType: 'xs:date',
              templateData: 'memoWithFormattedDate'
            }
          ]
        },
        {
          iniSection: 'INCARICO',
          iniKey: 'IndirizzoCliente',
          subfields: [
            {
              type: 'date',
              dateFormat: 'dd.MM.yyyy',
              memoFieldType: 'xs:date',
              templateData: 'memoWithInvalidDate'
            }
          ]
        },
        {
          iniSection: 'INCARICO',
          iniKey: 'NumeroPolizzaCliente',
          subfields: [
            {
              type: 'integer',
              templateData: 'memoWithInteger',
              memoFieldType: 'xs:integer',
              dossierData: 'Vehicle.SomeTestField'
            }
          ]
        },
        {
          iniSection: 'INCARICO',
          iniKey: 'NumeroPolizzaCliente',
          subfields: [
            {
              type: 'integer',
              thousandSeparator: ' ',
              memoFieldType: 'xs:integer',
              templateData: 'memoWithIntegerWithThousandSeparator',
              dossierData: 'Vehicle.SomeTestArrStr.[0]'
            }
          ]
        },
        {
          iniSection: 'INCARICO',
          iniKey: 'NumeroPolizzaCliente',
          subfields: [
            {
              type: 'float',
              thousandSeparator: ' ',
              floatDecimalSeparator: ',',
              floatFixedDecimalScale: 2,
              memoFieldType: 'xs:decimal',
              templateData: 'memoWithFloatWithThousandSeparator',
              dossierData: 'Vehicle.SomeTestArrObj.[0].float'
            }
          ]
        }
      ]
    },
    {
      companyData: {
        id: '03',
        description: 'COMPAGNIA',
        section: 'MITTENTE',
        key: 'TipoMittente',
        separator: '~',
        index: 0
      },
      contractBaseData: {
        contractType: 'vro_calculation',
        country: 'IT',
        language: 'it-IT',
        networkType: 'IFERT',
        templateId: 333333
      },
      fields: [
        {
          iniSection: 'DESTINATARIO',
          iniKey: 'TipoDestinatario',
          separator: '~',
          subfields: [
            {
              index: 0,
              type: 'string',
              templateData: 'memoWithSplitBySeparatorIdx0'
            },
            {
              index: 1,
              type: 'string',
              templateData: 'memoWithSplitBySeparatorIdx1'
            }
          ]
        }
      ]
    },
    {
      companyData: {
        id: '352',
        description: 'ALLIANZ VIVA',
        section: 'MESSAGGIO',
        key: 'Compagnia',
        separator: '~',
        index: 0
      },
      contractBaseData: {
        contractType: 'vro_calculation',
        country: 'IT',
        language: 'it-IT',
        networkType: 'IFERT',
        templateId: 111111
      },
      fields: [
        {
          iniSection: 'INCARICO',
          iniKey: 'tipoincarico',
          separator: '~',
          subfields: [
            {
              index: 0,
              type: 'string',
              templateData: 'memoWithSplitBySeparatorIdx0'
            },
            {
              index: 1,
              type: 'string',
              templateData: 'memoWithSplitBySeparatorIdx1'
            }
          ]
        }
      ]
    }
  ]
}
