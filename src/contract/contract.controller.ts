import {
  Body,
  Controller,
  Get,
  Inject,
  Post,
  ValidationPipe,
  Request,
  Query,
  BadRequestException,
  Put,
  Headers,
  UseGuards,
  UseInterceptors,
  UploadedFile
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiTags } from '@nestjs/swagger'
import { REQUEST } from '@nestjs/core'

import { HeadersNames } from 'src/common/constants/headers-names'
import { AuthGuard } from 'src/common/guards/auth.guard'
import { AuthService } from 'src/auth/auth.service'
import { ContractPartnersService } from 'src/contract/Partners/contract-partners.service'
import { ContractService } from './contract.service'
import { AdminAccountService } from 'src/admin-account/admin-account.service'

import { GetSubjectsDto } from 'src/address-book/dto/get-subjects.dto'
import { AssignPartnerToContractDto } from 'src/contract/Partners/dto/assign-partner-to-contract.dto'
import { UnAssignPartnerFromContractDto } from 'src/contract/Partners/dto/un-assign-partner-from-contract.dto'

import { parseAttachmentBinaryData } from 'src/common/utils/attachments/parseAttachmentBinaryData'

import { contractTag } from '../common/constants/swagger'
import { postPaymentAttachmentFolderId, postPaymentAttachmentName } from './constants'

import { HttpHeaders } from 'src/common/types/common'
import { PayloadForCreateClaimWithAnotherAccount, PayloadForPhotoUpload, PayloadForUpdateContract } from './types'
import { UserConfigService } from 'src/user-config/user-config.service'
import { MyClaimService } from 'src/my-claim/my-claim.service'
import { ProxyRequest } from 'src/myClaim-proxy/types'

@ApiTags(contractTag)
@Controller(contractTag)
export class ContractController {
  constructor(
    @Inject(REQUEST) private request: Request,
    private authService: AuthService,
    private contractPartnersService: ContractPartnersService,
    private adminAccountService: AdminAccountService,
    private contractService: ContractService,
    private userConfigService: UserConfigService,
    private myClaimService: MyClaimService
  ) {
    this.customerNumber = this.authService.decodeToken(request.headers[HeadersNames.DatAuthToken]).customerNumber
  }

  customerNumber = ''

  @Post('/assignPartnerToContract')
  assignPartnerToContract(@Body(ValidationPipe) assignPartnerToContractDto: AssignPartnerToContractDto): Promise<void> {
    return this.contractPartnersService.assignPartnerToContract(assignPartnerToContractDto)
  }

  @Post('/unAssignPartnerFromContract')
  unAssignPartnerFromContract(
    @Body(ValidationPipe) unAssignPartnerFromContractDto: UnAssignPartnerFromContractDto
  ): Promise<void> {
    return this.contractPartnersService.unAssignPartnerFromContract(unAssignPartnerFromContractDto)
  }

  @Post('/createClaimWithAnotherAccount')
  createClaimWithAnotherAccount(@Body() data: PayloadForCreateClaimWithAnotherAccount[]) {
    return this.contractService.createClaimWithAnotherAccount(data)
  }

  @Post('/upsert')
  createOrUpdateContract(@Body() data: PayloadForUpdateContract) {
    return this.contractService.createContract(data)
  }

  @Put('/upsert/proxy')
  async upsertClaim(@Body() body: ProxyRequest, @Headers() reqHeaders: HeadersNames) {
    return this.contractService.createOrUpdateContractWithProxy(body, reqHeaders[HeadersNames.DatAuthToken])
  }

  @Post('/upload-photo')
  uploadPhoto(@Body() input: PayloadForPhotoUpload) {
    return this.contractService.uploadPhoto(input)
  }

  @Post('/upload-attachment')
  async uploadAttachment(@Body() data: DAT.Request.UploadAttachmentByFolderID): Promise<boolean> {
    return this.adminAccountService.uploadCustomerAttachmentByFolderID({
      customerNumber: data.customerNumber || 3131411,
      attachmentItem: {
        fileName: data.attachmentName || postPaymentAttachmentName,
        documentID: data.documentID || postPaymentAttachmentFolderId,
        binaryData: data.attachmentItem
          ? Buffer.from(JSON.stringify(data.attachmentItem)).toString('base64')
          : Buffer.from(JSON.stringify(data)).toString('base64')
      }
    })
  }

  @Get('/post-payment')
  async getPostPayment(@Body() getSubjectsDto: GetSubjectsDto) {
    try {
      const { binaryData } = await this.adminAccountService.getCustomerAttachmentByName({
        customerNumber: getSubjectsDto?.networkOwnerCustomerNumber || this.customerNumber,
        name: postPaymentAttachmentName
      })

      return parseAttachmentBinaryData(binaryData, 'hjson')
    } catch (error) {
      if (error.status === 404) {
        return {}
      } else {
        throw new Error(error)
      }
    }
  }

  @Get('/get-contract')
  async getContract(@Query('contractId') contractId: number, @Query('customerNumber') customerNumber: number) {
    const currentContract = await this.myClaimService.getContract({ contractId })

    const parentConfig = await this.userConfigService.getUserConfigOnAuth(+this.customerNumber || customerNumber)

    const config = await this.userConfigService.getConfigByCustomerNumber(+this.customerNumber || customerNumber)

    const shownAPIFields =
      config?.config?.settings?.contractAPIShownFields || parentConfig?.config?.settings?.contractAPIShownFields

    if (!shownAPIFields) {
      throw new BadRequestException('contractAPIShownFields are not specified in the config')
    }

    return this.contractService.excludeHiddenFieldsInContract(currentContract, shownAPIFields)
  }

  @UseGuards(AuthGuard)
  @Post('/import-ini')
  @UseInterceptors(FileInterceptor('file'))
  importIni(
    @UploadedFile() file: Express.Multer.File,
    @Headers() headers: HttpHeaders,
    @Query('withDetailedResult', new ValidationPipe({ transform: true })) withDetailedResult?: boolean,
    @Query('templateId', new ValidationPipe({ transform: true })) templateId?: number
  ) {
    return this.contractService.importIni(file, {
      withDetailedResult,
      templateId,
      token: headers[HeadersNames.DatAuthToken]
    })
  }
}
