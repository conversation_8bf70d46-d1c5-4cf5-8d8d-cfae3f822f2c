import { CredentialsDto } from 'src/common/dto/credentials.dto'

export type PayloadForGetConfiguration = {
  customerNumber: CredentialsDto['customerNumber']
  old?: boolean
}

export interface PayloadForUpdateProfiles {
  customerNumber: CredentialsDto['customerNumber']
  value: DAT.Profiles
}

export interface PayloadForUpdateDates {
  customerNumber: CredentialsDto['customerNumber']
  username?: string
  value: DAT.AppointmentDates[] | DAT.ExpiryClaimDates[]
  key?: FieldsKey
}

export interface PayloadForGetUsersSettings {
  customerNumber: CredentialsDto['customerNumber']
  username: string
}

export interface PayloadForUpdateUsersSettings {
  customerNumber: CredentialsDto['customerNumber']
  username: string
  fieldPath: string // Ex. 'inbox.euroSvFilter'
  fieldValue: DAT.FieldValue
}

export interface PayloadForMultiUpdateUsersSettings {
  customerNumber: CredentialsDto['customerNumber']
  username: string
  settings: Record<DAT.Plugin, DAT.FieldValue> // Ex. '{ inbox: { euroSvFilter: true, secondSetting: 'test' } }
}

export interface PostIncrementalData {
  customerNumber: CredentialsDto['customerNumber']
  username: string
  value: DAT.IncrementalNumber['incrementalNumber']
}
export interface AutoincrementIncrementalNumberData {
  customerNumber: CredentialsDto['customerNumber']
  username?: string
}
export interface PayloadForUpdateIncrementalData {
  customerNumber: CredentialsDto['customerNumber']
  username?: string
  value: {
    value: DAT.IncrementalNumber['incrementalNumber']
  }
  key: FieldsKey
}

export interface QueryAppointments {
  customerNumber: string
  username?: string
}

export enum FieldsKey {
  INCREMENTAL_NUMBER = 'incrementalNumber',
  USERS_SETTINGS = 'usersSettings',
  INI_MAPPING = 'iniMapping',
  APPOINTMENT_DATES = 'appointmentDates',
  EXPIRY_CLAIMS_DATE = 'expiryClaimsDate',
  ADDRESS_BOOK = 'addressBook',
  PROFILES = 'profiles',
  PATTERN_INCREMENTAL_NUMBER = 'patternIncrementalNumber',
  PARCELLA_CONFIG = 'parcellaConfig',
  ACTIVITY_TRACKING = 'activityTracking',
  ROLES = 'roles',
  PRINTOUT = 'printout',
  SCENARIOS = 'scenarios',
  BID_MAIL_LIST = 'bidMailList',
  HAIL_DAMAGES = 'hailDamages',
  SMART_REPAIR = 'smartRepair'
}

export enum patternsIncrementalNumber {
  DEFAULT = 'yyyy.nr',
  UNIQ = 'nr',
  UNIQ_PER_YEAR = 'yyyy.nr',
  UNIQ_PER_MOUNTH = 'mmyyyy.nr',
  UNIQ_PER_MOUNTH_REVERSE = 'yyyymm.nr',
  UNIQ_PER_DAY = 'ddmmyyyy.nr',
  UNIQ_PER_DAY_REVERSE = 'yyyymmdd.nr'
}

export enum patternsPeriodIncrementalNumber {
  UNIQ_PER_DAY_REVERSE = 'yyyymmdd',
  UNIQ_PER_DAY = 'ddmmyyyy',
  UNIQ_PER_MOUNTH_REVERSE = 'yyyymm',
  UNIQ_PER_MOUNTH = 'mmyyyy',
  UNIQ_PER_YEAR = 'yyyy',
  DEFAULT = 'yyyy'
}

export enum patternsUserIncrementalNumber {
  DEFAULT = 'username'
}
export enum patternsValueIncrementalNumber {
  DEFAULT = 'nr'
}

export interface DecodedToken {
  clnt: string
  user: string
}
