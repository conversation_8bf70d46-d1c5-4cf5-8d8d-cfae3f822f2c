import { AxiosError } from 'axios'
import { parseSoapResponse } from 'src/shared/soap/helpers/parseSoapResponse'

export function matchedBracket(message: string) {
  return message.match(/<|&lt;/)
}

export function getErrorInformation(message: string) {
  const matchBracket = matchedBracket(message)

  return {
    message: matchBracket ? message.slice(0, matchBracket.index).trim() : message,
    errorBlock: matchBracket && message.slice(matchBracket?.index).replace(/&lt;/gi, '<').replace(/&gt;/gi, '>')
  }
}

export function getErrorDataFromApi2Dat5Error(error: Error) {
  const isErrorThisAxiosResponse =
    'response' in error && typeof error.response === 'object' && error.response && 'data' in error.response
  const axiosResponse = ((error as unknown as AxiosError).response?.data?.toString() || '') as string

  const errorResponse = getErrorInformation(isErrorThisAxiosResponse ? axiosResponse : error.message)

  if (errorResponse.errorBlock) {
    const { faultstring, faultcode } = parseSoapResponse<{ faultcode: string; faultstring: unknown }>(
      errorResponse.errorBlock
    )
    return { errorCode: 500, errorType: faultcode, errorMessage: faultstring }
  } else {
    return { errorCode: (error as unknown as AxiosError)?.code, errorMessage: errorResponse.message }
  }
}
